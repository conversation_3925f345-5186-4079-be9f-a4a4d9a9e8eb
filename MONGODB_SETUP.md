# 🗄️ MongoDB Setup Instructions

## ✅ **Current Configuration**

Your MongoDB connection string has been updated to:
```
mongodb+srv://ziqra5177:<db_password>@cluster0.kqqokvj.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0
```

## 🔧 **To Complete Setup:**

### Step 1: Replace the Password
1. Open `backend/.env` file
2. Find the line with `MONGODB_URI=`
3. Replace `<db_password>` with your actual MongoDB Atlas password

**Example:**
```env
# Before:
MONGODB_URI=mongodb+srv://ziqra5177:<db_password>@cluster0.kqqokvj.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0

# After (replace 'yourpassword' with your actual password):
MONGODB_URI=mongodb+srv://ziqra5177:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
```

### Step 2: Test the Connection
Run the test script to verify your connection:
```bash
node test-mongodb.js
```

### Step 3: Start Your Applications
Use any of these methods:
- Double-click `START_ALL.bat` (recommended)
- Double-click individual `.bat` files
- Use manual commands from `START_APPLICATIONS.md`

## 🔒 **Security Notes:**

- ✅ Your connection string is properly configured for MongoDB Atlas
- ✅ The connection includes retry writes and majority write concern
- ✅ App name is set to "Cluster0" for identification
- ⚠️ **Important**: Never commit your actual password to version control

## 🌐 **Database Details:**

- **Cluster**: cluster0.kqqokvj.mongodb.net
- **Username**: ziqra5177
- **Database**: Will be created automatically when first used
- **Collections**: Users, Tasks (created automatically)

## 🚀 **Ready to Go!**

Once you replace `<db_password>` with your actual password, your TaskManager application will be fully connected to MongoDB Atlas and ready to use!
