# 🚀 How to Start Your Applications

You have **TWO** applications in this workspace:

## 1. 🍕 Food App (Next.js)
**Location**: Root directory  
**Port**: 3000  
**URL**: http://localhost:3000

### To Start:
```bash
npm run dev
```

### Features:
- Modern food delivery website
- Responsive design with Tailwind CSS
- Homepage with hero section and featured dishes
- Menu page with food items
- Restaurants page with search and filtering

---

## 2. 📋 TaskManager (MERN Stack)
**Backend Port**: 5000  
**Frontend Port**: 5173  
**Frontend URL**: http://localhost:5173

### To Start Backend:
```bash
cd backend
node server.js
```

### To Start Frontend:
```bash
cd frontend
npm run dev
```

### Features:
- Complete MERN stack application
- User authentication with JWT
- Task management with CRUD operations
- MongoDB database integration
- Responsive React frontend with Tailwind CSS
- Protected routes and user sessions

---

## 🔧 Quick Setup Commands

### Option 1: Use the batch files
- Double-click `run-backend.bat` to start TaskManager backend
- Double-click `run-frontend.bat` to start TaskManager frontend  
- Double-click `run-food-app.bat` to start Food App

### Option 2: Manual commands
Open 3 separate terminals:

**Terminal 1 - TaskManager Backend:**
```bash
cd backend
node server.js
```

**Terminal 2 - TaskManager Frontend:**
```bash
cd frontend
npm run dev
```

**Terminal 3 - Food App:**
```bash
npm run dev
```

---

## 🌐 Access Your Applications

1. **Food App**: http://localhost:3000
2. **TaskManager**: http://localhost:5173

Both applications are fully functional and ready for deployment!

---

## 📊 Database Configuration

The TaskManager uses MongoDB Atlas with your cluster:
- **Cluster**: cluster0.kqqokvj.mongodb.net
- **Database**: taskmanager
- **Connection**: Already configured in backend/.env

---

## 🚀 Ready for Deployment

Both applications are deployment-ready:
- **Food App**: Can be deployed to Vercel
- **TaskManager**: Backend to Render, Frontend to Vercel
