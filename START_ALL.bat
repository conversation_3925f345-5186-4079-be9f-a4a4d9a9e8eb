@echo off
color 0A
echo.
echo ========================================
echo    🚀 STARTING ALL APPLICATIONS 🚀
echo ========================================
echo.

echo 📋 Starting TaskManager Backend (Port 5000)...
start "TaskManager Backend" cmd /k "cd /d "C:\Users\<USER>\Desktop\Food App\backend" && node server.js"

timeout /t 3 /nobreak >nul

echo 🎨 Starting TaskManager Frontend (Port 5173)...
start "TaskManager Frontend" cmd /k "cd /d "C:\Users\<USER>\Desktop\Food App\frontend" && npm run dev"

timeout /t 3 /nobreak >nul

echo 🍕 Starting Food App (Port 3000)...
start "Food App" cmd /k "cd /d "C:\Users\<USER>\Desktop\Food App" && npm run dev"

echo.
echo ✅ All applications are starting!
echo.
echo 🌐 Your applications will be available at:
echo    📋 TaskManager: http://localhost:5173
echo    🍕 Food App: http://localhost:3000
echo    🔧 Backend API: http://localhost:5000
echo.
echo 💡 Wait 30-60 seconds for all servers to fully start
echo.
pause
