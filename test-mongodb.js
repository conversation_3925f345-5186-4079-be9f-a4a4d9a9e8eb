/**
 * MongoDB Connection Test Script
 * Tests the MongoDB Atlas connection specifically
 */

// Load environment variables from backend directory
require('dotenv').config({ path: './backend/.env' });

// Check if we're in the right directory
const fs = require('fs');
const path = require('path');

if (!fs.existsSync('./backend/.env')) {
  console.log('❌ backend/.env file not found');
  console.log('💡 Make sure you run this from the project root directory');
  process.exit(1);
}

const mongoose = require('./backend/node_modules/mongoose');

async function testMongoDB() {
  console.log('🍃 MongoDB Atlas Connection Test');
  console.log('================================');
  console.log('');

  // Check if environment variables are loaded
  if (!process.env.MONGODB_URI) {
    console.log('❌ MONGODB_URI not found in environment variables');
    console.log('💡 Make sure backend/.env file exists and contains MONGODB_URI');
    return;
  }

  // Check if password placeholder is still there
  if (process.env.MONGODB_URI.includes('your_actual_password_here')) {
    console.log('❌ MongoDB password not configured');
    console.log('💡 Please replace "your_actual_password_here" with your actual password in backend/.env');
    console.log('');
    console.log('Current MONGODB_URI:');
    console.log(process.env.MONGODB_URI);
    return;
  }

  console.log('🔗 Connection Details:');
  console.log('Cluster: cluster0.kqqokvj.mongodb.net');
  console.log('Username: ziqra5177');
  console.log('Database: taskmanager');
  console.log('');

  try {
    console.log('⏳ Connecting to MongoDB Atlas...');
    
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log('✅ MongoDB Connected Successfully!');
    console.log('📍 Host:', conn.connection.host);
    console.log('🗄️  Database:', conn.connection.name);
    console.log('🔌 Ready State:', conn.connection.readyState === 1 ? 'Connected' : 'Not Connected');
    console.log('');

    // Test basic operations
    console.log('🧪 Testing basic operations...');
    
    // Test if we can access the database
    const collections = await conn.connection.db.listCollections().toArray();
    console.log('📋 Available collections:', collections.length);
    
    if (collections.length > 0) {
      console.log('   Collections:', collections.map(c => c.name).join(', '));
    } else {
      console.log('   No collections yet (will be created when data is added)');
    }

    console.log('');
    console.log('🎉 MongoDB Atlas is ready for the TaskManager application!');
    
  } catch (error) {
    console.log('❌ MongoDB connection failed!');
    console.log('');
    console.log('🔍 Error details:', error.message);
    console.log('');

    // Provide specific troubleshooting advice
    if (error.message.includes('authentication failed')) {
      console.log('💡 Troubleshooting: Authentication Failed');
      console.log('   - Check your username: ziqra5177');
      console.log('   - Verify your password in backend/.env');
      console.log('   - Make sure the user exists in MongoDB Atlas Database Access');
    } else if (error.message.includes('network') || error.message.includes('timeout')) {
      console.log('💡 Troubleshooting: Network/Timeout Error');
      console.log('   - Check your internet connection');
      console.log('   - Verify Network Access settings in MongoDB Atlas');
      console.log('   - Make sure your IP address is whitelisted');
      console.log('   - Try adding 0.0.0.0/0 for development (not recommended for production)');
    } else if (error.message.includes('ENOTFOUND')) {
      console.log('💡 Troubleshooting: DNS/Host Not Found');
      console.log('   - Check the cluster URL: cluster0.kqqokvj.mongodb.net');
      console.log('   - Verify your internet connection');
      console.log('   - Make sure the cluster is running in MongoDB Atlas');
    } else {
      console.log('💡 General troubleshooting:');
      console.log('   - Check MONGODB_SETUP_GUIDE.md for detailed instructions');
      console.log('   - Verify all settings in MongoDB Atlas');
      console.log('   - Try connecting with MongoDB Compass using the same connection string');
    }
  } finally {
    // Close the connection
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('');
      console.log('🔌 Connection closed');
    }
  }
}

// Run the test
testMongoDB().catch(console.error);
