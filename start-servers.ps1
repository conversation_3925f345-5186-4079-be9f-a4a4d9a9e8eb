# PowerShell script to start all servers

Write-Host "🚀 Starting TaskManager Backend Server..." -ForegroundColor Green
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'C:\Users\<USER>\Desktop\Food App\backend'; node server.js"

Start-Sleep -Seconds 3

Write-Host "🚀 Starting TaskManager Frontend Server..." -ForegroundColor Green  
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'C:\Users\<USER>\Desktop\Food App\frontend'; npm run dev"

Start-Sleep -Seconds 3

Write-Host "🚀 Starting Food App Server..." -ForegroundColor Green
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'C:\Users\<USER>\Desktop\Food App'; npm run dev"

Write-Host "✅ All servers are starting..." -ForegroundColor Green
Write-Host "📱 Food App: http://localhost:3000" -ForegroundColor Yellow
Write-Host "📋 TaskManager: http://localhost:5173" -ForegroundColor Yellow
Write-Host "🔧 Backend API: http://localhost:5000" -ForegroundColor Yellow
